import React, { Component, FC, ReactNode } from 'react';
import styles from './styles.module.scss';
import emptyImg from '@/assets/empty.png';

interface IProps {
  children?: string | ReactNode;
  img?: JSX.Element | string;
  className?: string;
  onClick?: () => void;
}

const SuperEmpty: FC<IProps> = (props) => {

  const { children, img, className, onClick } = props;

  return (
    <div className={`${styles['empty']} ${className || ''}`} onClick={onClick}>
      {img ? img : <img src={emptyImg} />}
      {children ? (
        <div className={styles['text']}>{children}</div>
      ) : (
        <div className={styles['text']}>暂无数据</div>
      )}
    </div>
  );
}

export default SuperEmpty;
