/**
 * 优化的横屏管理器
 * 基于精确容器定位和CSS transform实现强制横屏
 * 不依赖系统自动旋转，通过CSS实现H5强制横屏功能
 */

export class SimpleLandscapeManager {
  private isLandscapeMode: boolean = false;
  private listeners: Array<(isLandscape: boolean) => void> = [];
  private photoViewContainer: HTMLElement | null = null;
  private originalStyles: Map<string, string> = new Map();

  constructor() {
    this.setupFullscreenListener();
  }

  /**
   * 查找 react-photo-view 的容器元素
   * 优先级：.PhotoView-Portal > [class*="PhotoView"] > body
   */
  private findPhotoViewContainer(): HTMLElement | null {
    // 尝试多种可能的选择器，按优先级排序
    const selectors = [
      '.PhotoView-Portal',
      '[class*="PhotoView-Portal"]',
      '.PhotoView-Slider',
      '[class*="PhotoView-Slider"]',
      '.PhotoView',
      '[class*="PhotoView"]',
      '.react-photo-view',
      '[data-photo-view]'
    ];

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector) as NodeListOf<HTMLElement>;

      // 如果找到多个元素，选择最后一个（通常是最新打开的）
      if (elements.length > 0) {
        const element = elements[elements.length - 1];

        // 验证元素是否可见且有合理的尺寸
        if (element.offsetWidth > 0 && element.offsetHeight > 0) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`找到 PhotoView 容器: ${selector}`, element);
          }
          return element;
        }
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.warn('未找到 PhotoView 容器，使用 body 作为后备方案');
    }

    // 如果都找不到，返回 body 作为后备方案
    return document.body;
  }

  /**
   * 设置全屏监听器 - 退出全屏时自动退出横屏
   */
  private setupFullscreenListener(): void {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && this.isLandscapeMode) {
        this.exitLandscape();
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
  }

  /**
   * 添加状态变化监听器
   */
  addListener(callback: (isLandscape: boolean) => void): void {
    this.listeners.push(callback);
  }

  /**
   * 移除状态变化监听器
   */
  removeListener(callback: (isLandscape: boolean) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知状态变化
   */
  private notifyChange(): void {
    this.listeners.forEach(callback => {
      try {
        callback(this.isLandscapeMode);
      } catch (error) {
        console.error('横屏状态监听器执行失败:', error);
      }
    });
  }

  /**
   * 检查是否在横屏模式
   */
  isInLandscape(): boolean {
    return this.isLandscapeMode;
  }

  /**
   * 切换横屏模式
   * 添加延迟检测机制，确保 PhotoView 容器已经渲染
   */
  toggleLandscape(): void {
    if (this.isLandscapeMode) {
      this.exitLandscape();
    } else {
      // 延迟执行，确保 PhotoView 容器已经渲染
      setTimeout(() => {
        this.enterLandscape();
      }, 100);
    }
  }

  /**
   * 强制刷新横屏状态
   * 用于在 PhotoView 打开后重新应用横屏效果
   */
  refreshLandscape(): void {
    if (this.isLandscapeMode) {
      // 重新查找容器并应用样式
      const newContainer = this.findPhotoViewContainer();
      if (newContainer && newContainer !== this.photoViewContainer) {
        // 如果找到了新的容器，更新引用并应用样式
        this.photoViewContainer = newContainer;
        this.saveOriginalStyles(this.photoViewContainer);
        this.applyLandscapeTransform(this.photoViewContainer);
        this.photoViewContainer.classList.add('landscape-mode');

        if (process.env.NODE_ENV === 'development') {
          console.log('刷新横屏模式，新容器:', this.photoViewContainer?.className);
        }
      }
    }
  }

  /**
   * 保存元素的原始样式
   */
  private saveOriginalStyles(element: HTMLElement): void {
    const stylesToSave = ['transform', 'transformOrigin', 'width', 'height', 'position', 'top', 'left'];
    stylesToSave.forEach(prop => {
      const value = element.style.getPropertyValue(prop) || '';
      this.originalStyles.set(prop, value);
    });
  }

  /**
   * 恢复元素的原始样式
   */
  private restoreOriginalStyles(element: HTMLElement): void {
    this.originalStyles.forEach((value, prop) => {
      if (value) {
        element.style.setProperty(prop, value);
      } else {
        element.style.removeProperty(prop);
      }
    });
    this.originalStyles.clear();
  }

  /**
   * 重置可能冲突的样式
   */
  private resetConflictingStyles(element: HTMLElement): void {
    // 重置可能影响变换的样式
    element.style.removeProperty('transform');
    element.style.removeProperty('transform-origin');
    element.style.removeProperty('width');
    element.style.removeProperty('height');
    element.style.removeProperty('position');
    element.style.removeProperty('top');
    element.style.removeProperty('left');
    element.style.removeProperty('right');
    element.style.removeProperty('bottom');
    element.style.removeProperty('margin');
    element.style.removeProperty('margin-top');
    element.style.removeProperty('margin-left');
    element.style.removeProperty('margin-right');
    element.style.removeProperty('margin-bottom');
  }

  /**
   * 应用横屏变换样式
   * 简化变换逻辑，使用更可靠的CSS方案
   */
  private applyLandscapeTransform(element: HTMLElement): void {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 先重置可能冲突的样式
    this.resetConflictingStyles(element);

    // 使用简化的变换方案
    // 直接旋转90度，让CSS处理布局
    element.style.setProperty('transform', 'rotate(90deg)', 'important');
    element.style.setProperty('transform-origin', '50% 50%', 'important');
    element.style.setProperty('width', '100vh', 'important');
    element.style.setProperty('height', '100vw', 'important');
    element.style.setProperty('position', 'fixed', 'important');
    element.style.setProperty('top', '50%', 'important');
    element.style.setProperty('left', '50%', 'important');
    element.style.setProperty('margin-top', '-50vw', 'important');
    element.style.setProperty('margin-left', '-50vh', 'important');
    element.style.setProperty('z-index', '9999', 'important');
    element.style.setProperty('overflow', 'hidden', 'important');

    if (process.env.NODE_ENV === 'development') {
      console.log('应用简化横屏变换:', {
        viewportWidth,
        viewportHeight,
        element: element.className
      });
    }
  }

  /**
   * 进入横屏模式
   */
  enterLandscape(): void {
    if (this.isLandscapeMode) return;

    try {
      // 查找 PhotoView 容器
      this.photoViewContainer = this.findPhotoViewContainer();

      if (this.photoViewContainer) {
        // 保存原始样式
        this.saveOriginalStyles(this.photoViewContainer);

        // 应用横屏变换
        this.applyLandscapeTransform(this.photoViewContainer);

        // 添加横屏模式类名用于额外的样式控制
        this.photoViewContainer.classList.add('landscape-mode');
      }

      // 在 body 上也添加类名，用于全局样式调整
      document.body.classList.add('photo-landscape-mode');

      this.isLandscapeMode = true;
      this.notifyChange();

      if (process.env.NODE_ENV === 'development') {
        console.log('进入横屏模式，容器:', this.photoViewContainer?.className);
      }
    } catch (error) {
      console.error('进入横屏模式失败:', error);
    }
  }

  /**
   * 退出横屏模式
   */
  exitLandscape(): void {
    if (!this.isLandscapeMode) return;

    try {
      if (this.photoViewContainer) {
        // 恢复原始样式
        this.restoreOriginalStyles(this.photoViewContainer);

        // 移除横屏模式类名
        this.photoViewContainer.classList.remove('landscape-mode');
        this.photoViewContainer = null;
      }

      // 移除 body 上的类名
      document.body.classList.remove('photo-landscape-mode');

      this.isLandscapeMode = false;
      this.notifyChange();

      if (process.env.NODE_ENV === 'development') {
        console.log('退出横屏模式');
      }
    } catch (error) {
      console.error('退出横屏模式失败:', error);
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.exitLandscape();
    this.listeners = [];
  }
}

// 全局实例
export const simpleLandscapeManager = new SimpleLandscapeManager();

// 工具函数
export const isLandscapeSupported = (): boolean => {
  return true; // CSS类名切换总是支持的
};

export const getCurrentOrientation = (): string => {
  if (typeof window === 'undefined') return 'portrait';

  // 优先使用现代 API
  if (window.screen && window.screen.orientation) {
    return window.screen.orientation.type;
  }

  if (screen.orientation) {
    return screen.orientation.type;
  }

  // 使用废弃的 API 作为后备方案，但抑制 TypeScript 警告
  // @ts-ignore - window.orientation is deprecated but still needed for older browsers
  if (typeof window.orientation !== 'undefined') {
    // @ts-ignore
    return Math.abs(window.orientation) === 90 ? 'landscape-primary' : 'portrait-primary';
  }

  // 最后的后备方案：根据窗口尺寸判断
  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
};

// 检查是否为横屏
export const isLandscape = (): boolean => {
  if (simpleLandscapeManager.isInLandscape()) {
    return true;
  }
  
  if (typeof window === 'undefined') return false;
  
  const orientation = getCurrentOrientation();
  return orientation.includes('landscape');
};
