/**
 * 重构的横屏管理器
 * 基于CSS类名切换，不动态注入样式
 * 所有横屏样式都在静态CSS文件中定义
 */

export class SimpleLandscapeManager {
  private isLandscapeMode: boolean = false;
  private listeners: Array<(isLandscape: boolean) => void> = [];

  constructor() {
    this.setupFullscreenListener();
  }

  /**
   * 设置全屏监听器 - 退出全屏时自动退出横屏
   */
  private setupFullscreenListener(): void {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement && this.isLandscapeMode) {
        this.exitLandscape();
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
  }

  /**
   * 添加状态变化监听器
   */
  addListener(callback: (isLandscape: boolean) => void): void {
    this.listeners.push(callback);
  }

  /**
   * 移除状态变化监听器
   */
  removeListener(callback: (isLandscape: boolean) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知状态变化
   */
  private notifyChange(): void {
    this.listeners.forEach(callback => {
      try {
        callback(this.isLandscapeMode);
      } catch (error) {
        console.error('横屏状态监听器执行失败:', error);
      }
    });
  }

  /**
   * 检查是否在横屏模式
   */
  isInLandscape(): boolean {
    return this.isLandscapeMode;
  }

  /**
   * 切换横屏模式
   */
  toggleLandscape(): void {
    if (this.isLandscapeMode) {
      this.exitLandscape();
    } else {
      this.enterLandscape();
    }
  }

  /**
   * 进入横屏模式
   */
  enterLandscape(): void {
    if (this.isLandscapeMode) return;

    try {
      // 简单的CSS类切换
      document.documentElement.classList.add('landscape-mode');
      
      this.isLandscapeMode = true;
      this.notifyChange();

      if (process.env.NODE_ENV === 'development') {
        console.log('进入横屏模式');
      }
    } catch (error) {
      console.error('进入横屏模式失败:', error);
    }
  }

  /**
   * 退出横屏模式
   */
  exitLandscape(): void {
    if (!this.isLandscapeMode) return;

    try {
      // 移除CSS类
      document.documentElement.classList.remove('landscape-mode');
      
      this.isLandscapeMode = false;
      this.notifyChange();

      if (process.env.NODE_ENV === 'development') {
        console.log('退出横屏模式');
      }
    } catch (error) {
      console.error('退出横屏模式失败:', error);
    }
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.exitLandscape();
    this.listeners = [];
  }
}

// 全局实例
export const simpleLandscapeManager = new SimpleLandscapeManager();

// 工具函数
export const isLandscapeSupported = (): boolean => {
  return true; // CSS类名切换总是支持的
};

export const getCurrentOrientation = (): string => {
  if (typeof window === 'undefined') return 'portrait';
  
  if (window.screen && window.screen.orientation) {
    return window.screen.orientation.type;
  }
  
  if (window.orientation !== undefined) {
    return Math.abs(window.orientation) === 90 ? 'landscape-primary' : 'portrait-primary';
  }
  
  if (screen.orientation) {
    return screen.orientation.type;
  }
  
  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
};

// 检查是否为横屏
export const isLandscape = (): boolean => {
  if (simpleLandscapeManager.isInLandscape()) {
    return true;
  }
  
  if (typeof window === 'undefined') return false;
  
  const orientation = getCurrentOrientation();
  return orientation.includes('landscape');
};
