import React from 'react';
import SwiperImages from './index';

/**
 * 横屏功能测试组件
 * 用于验证优化后的横屏功能是否正常工作
 */
const LandscapeTest: React.FC = () => {
  // 测试图片数据
  const testImages = [
    {
      src: 'https://picsum.photos/800/600?random=1',
      thumbnail: 'https://picsum.photos/200/150?random=1',
      alt: '测试图片 1'
    },
    {
      src: 'https://picsum.photos/800/600?random=2',
      thumbnail: 'https://picsum.photos/200/150?random=2',
      alt: '测试图片 2'
    },
    {
      src: 'https://picsum.photos/800/600?random=3',
      thumbnail: 'https://picsum.photos/200/150?random=3',
      alt: '测试图片 3'
    }
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h2>横屏功能测试</h2>
      <p>点击图片打开预览，然后点击横屏按钮测试横屏功能</p>
      
      <SwiperImages
        images={testImages}
        height="300px"
        showToolbar={true}
        showLandscapeButton={true}
        showFullscreenButton={true}
        showViewerPlayButton={true}
        loop={true}
        showIndicator={true}
      />
      
      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>测试说明：</h3>
        <ul>
          <li>点击任意图片打开 PhotoView 预览</li>
          <li>在预览模式下，点击横屏按钮（📱图标）</li>
          <li>应该看到图片旋转90度并适应横屏布局</li>
          <li>再次点击横屏按钮应该恢复竖屏模式</li>
          <li>关闭预览时会自动退出横屏模式</li>
        </ul>
        
        <h3>预期效果：</h3>
        <ul>
          <li>横屏时图片容器旋转90度</li>
          <li>工具栏位置调整到合适位置</li>
          <li>图片保持居中显示</li>
          <li>不影响 react-photo-view 的缩放、拖拽等功能</li>
          <li>窗口大小变化时横屏效果自动适应</li>
        </ul>
      </div>
    </div>
  );
};

export default LandscapeTest;
