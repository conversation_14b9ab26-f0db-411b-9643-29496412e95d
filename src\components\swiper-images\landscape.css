/**
 * react-photo-view 横屏模式样式
 * 实现基于 CSS transform 的强制横屏功能
 * 不依赖系统自动旋转，适用于 H5 环境
 */

/* 全局横屏模式样式 */
body.photo-landscape-mode {
  overflow: hidden;
  /* 防止页面滚动 */
  position: fixed;
  width: 100%;
  height: 100%;
}

/* PhotoView 容器横屏样式 */
.landscape-mode {
  /* 基础变换已通过 JS 动态设置 */
  z-index: 9999 !important;
  /* 确保在最顶层 */
  overflow: hidden !important;
  /* 防止内容溢出 */
  background: rgba(0, 0, 0, 0.9) !important;
  /* 确保背景正确 */
}

/* 针对 react-photo-view 的特定样式优化 */
.landscape-mode .PhotoView-Slider,
.landscape-mode [class*="PhotoView-Slider"],
.landscape-mode [class*="slider"] {
  /* 调整滑动容器 */
  width: 100% !important;
  height: 100% !important;
  position: relative !important;
  overflow: hidden !important;
}

.landscape-mode .PhotoView-PhotoWrap,
.landscape-mode [class*="PhotoView-PhotoWrap"],
.landscape-mode [class*="photo-wrap"] {
  /* 调整图片包装器 */
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  overflow: hidden !important;
}

.landscape-mode .PhotoView-Photo,
.landscape-mode [class*="PhotoView-Photo"],
.landscape-mode [class*="photo"] img {
  /* 调整图片本身 */
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  display: block !important;
  margin: 0 auto !important;
}

/* 确保所有可能的图片元素都被正确处理 */
.landscape-mode img {
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
  display: block !important;
  margin: 0 auto !important;
}

/* 修复可能的布局问题 */
.landscape-mode * {
  box-sizing: border-box !important;
}

/* 强制重置可能影响布局的样式 */
.landscape-mode,
.landscape-mode * {
  /* 重置可能的变换 */
  transform-style: flat !important;
  backface-visibility: hidden !important;
  /* 确保渲染性能 */
  will-change: auto !important;
}

/* 特别针对图片查看器的背景和容器 */
.landscape-mode .PhotoView-Portal,
.landscape-mode [class*="PhotoView-Portal"],
.landscape-mode [class*="portal"] {
  background: rgba(0, 0, 0, 0.9) !important;
  backdrop-filter: none !important;
}

/* 确保工具栏和控件正确显示 */
.landscape-mode .PhotoView-Toolbar,
.landscape-mode [class*="PhotoView-Toolbar"],
.landscape-mode [class*="toolbar"] {
  position: absolute !important;
  z-index: 10000 !important;
}

/* 工具栏在横屏模式下的调整 */
.landscape-mode .PhotoView-Toolbar,
.landscape-mode [class*="toolbar"],
.landscape-mode [class*="Toolbar"] {
  /* 工具栏位置调整 */
  position: absolute !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 10000 !important;
  background: rgba(0, 0, 0, 0.7) !important;
  border-radius: 8px !important;
  padding: 8px !important;
}

/* 导航按钮在横屏模式下的调整 */
.landscape-mode .PhotoView-ArrowLeft,
.landscape-mode .PhotoView-ArrowRight,
.landscape-mode [class*="arrow"],
.landscape-mode [class*="Arrow"] {
  /* 导航箭头位置调整 */
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10000 !important;
}

.landscape-mode .PhotoView-ArrowLeft,
.landscape-mode [class*="arrow"][class*="left"] {
  left: 20px !important;
}

.landscape-mode .PhotoView-ArrowRight,
.landscape-mode [class*="arrow"][class*="right"] {
  right: 20px !important;
}

/* 关闭按钮在横屏模式下的调整 */
.landscape-mode .PhotoView-Close,
.landscape-mode [class*="close"],
.landscape-mode [class*="Close"] {
  position: absolute !important;
  top: 20px !important;
  left: 20px !important;
  z-index: 10000 !important;
  background: rgba(0, 0, 0, 0.7) !important;
  border-radius: 50% !important;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
}

/* 背景遮罩在横屏模式下的调整 */
.landscape-mode .PhotoView-Backdrop,
.landscape-mode [class*="backdrop"],
.landscape-mode [class*="Backdrop"],
.landscape-mode [class*="mask"] {
  background: rgba(0, 0, 0, 0.9) !important;
}

/* 加载状态在横屏模式下的调整 */
.landscape-mode .PhotoView-Loading,
.landscape-mode [class*="loading"],
.landscape-mode [class*="Loading"] {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 10000 !important;
}

/* 自定义工具栏按钮在横屏模式下的样式 */
.landscape-mode .customToolbar {
  position: absolute !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 10000 !important;
  background: rgba(0, 0, 0, 0.7) !important;
  border-radius: 25px !important;
  padding: 10px 20px !important;
  display: flex !important;
  gap: 15px !important;
}

.landscape-mode .customToolbar .toolbarButton {
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  border-radius: 50% !important;
  width: 44px !important;
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.landscape-mode .customToolbar .toolbarButton:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.1) !important;
}

.landscape-mode .customToolbar .toolbarButton:active {
  transform: scale(0.95) !important;
}

/* 横屏模式下的动画过渡 */
.landscape-mode {
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

/* 确保在横屏模式下禁用页面滚动 */
body.photo-landscape-mode {
  touch-action: none;
  -webkit-overflow-scrolling: touch;
}

/* 针对不同设备的适配 */
@media screen and (max-width: 768px) {
  .landscape-mode .customToolbar {
    bottom: 15px !important;
    padding: 8px 16px !important;
  }
  
  .landscape-mode .customToolbar .toolbarButton {
    width: 40px !important;
    height: 40px !important;
  }
  
  .landscape-mode .PhotoView-Close {
    width: 36px !important;
    height: 36px !important;
    top: 15px !important;
    left: 15px !important;
  }
  
  .landscape-mode .PhotoView-Toolbar {
    top: 15px !important;
    right: 15px !important;
    padding: 6px !important;
  }
}

/* 高分辨率屏幕适配 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .landscape-mode .PhotoView-Photo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 确保横屏模式下的触摸体验 */
.landscape-mode * {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 允许图片和按钮的交互 */
.landscape-mode .PhotoView-Photo,
.landscape-mode .toolbarButton,
.landscape-mode .PhotoView-Close,
.landscape-mode .PhotoView-ArrowLeft,
.landscape-mode .PhotoView-ArrowRight {
  -webkit-user-select: auto;
  user-select: auto;
  pointer-events: auto;
}
