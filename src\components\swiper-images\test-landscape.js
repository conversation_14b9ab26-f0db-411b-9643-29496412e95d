/**
 * 横屏功能测试脚本
 * 在浏览器控制台中运行此脚本来测试横屏功能
 */

// 测试横屏管理器的基本功能
function testLandscapeManager() {
  console.log('开始测试横屏功能...');
  
  // 检查是否支持横屏
  console.log('横屏支持状态:', window.isLandscapeSupported ? window.isLandscapeSupported() : '未找到 isLandscapeSupported 函数');
  
  // 检查当前方向
  console.log('当前屏幕方向:', window.getCurrentOrientation ? window.getCurrentOrientation() : '未找到 getCurrentOrientation 函数');
  
  // 检查是否为横屏
  console.log('当前是否为横屏:', window.isLandscape ? window.isLandscape() : '未找到 isLandscape 函数');
  
  // 检查横屏管理器
  if (window.simpleLandscapeManager) {
    console.log('横屏管理器状态:', window.simpleLandscapeManager.isInLandscape());
    
    // 测试进入横屏
    console.log('测试进入横屏...');
    window.simpleLandscapeManager.enterLandscape();
    
    setTimeout(() => {
      console.log('横屏状态:', window.simpleLandscapeManager.isInLandscape());
      
      // 测试退出横屏
      console.log('测试退出横屏...');
      window.simpleLandscapeManager.exitLandscape();
      
      setTimeout(() => {
        console.log('最终横屏状态:', window.simpleLandscapeManager.isInLandscape());
      }, 500);
    }, 1000);
  } else {
    console.error('未找到 simpleLandscapeManager');
  }
}

// 测试容器检测
function testContainerDetection() {
  console.log('测试容器检测...');
  
  const selectors = [
    '.PhotoView-Portal',
    '[class*="PhotoView-Portal"]',
    '.PhotoView-Slider',
    '[class*="PhotoView-Slider"]',
    '.PhotoView',
    '[class*="PhotoView"]'
  ];
  
  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    console.log(`${selector}: 找到 ${elements.length} 个元素`, elements);
  });
}

// 测试样式应用
function testStyleApplication() {
  console.log('测试样式应用...');
  
  // 检查横屏样式文件是否加载
  const stylesheets = Array.from(document.styleSheets);
  const landscapeStyles = stylesheets.find(sheet => {
    try {
      return sheet.href && sheet.href.includes('landscape.css');
    } catch (e) {
      return false;
    }
  });
  
  console.log('横屏样式文件加载状态:', landscapeStyles ? '已加载' : '未找到');
  
  // 检查 body 类名
  console.log('body 类名:', document.body.className);
  
  // 检查是否有横屏相关的类名
  const hasLandscapeClass = document.body.classList.contains('photo-landscape-mode');
  console.log('body 是否包含横屏类名:', hasLandscapeClass);
}

// 模拟用户操作测试
function simulateUserInteraction() {
  console.log('模拟用户交互测试...');
  
  // 查找横屏按钮
  const landscapeButtons = document.querySelectorAll('[title*="横屏"], [title*="竖屏"]');
  console.log('找到横屏按钮数量:', landscapeButtons.length);
  
  if (landscapeButtons.length > 0) {
    const button = landscapeButtons[0];
    console.log('横屏按钮:', button);
    console.log('按钮标题:', button.title);
    
    // 模拟点击
    console.log('模拟点击横屏按钮...');
    button.click();
    
    setTimeout(() => {
      console.log('点击后的状态检查...');
      testStyleApplication();
    }, 500);
  } else {
    console.warn('未找到横屏按钮');
  }
}

// 综合测试函数
function runAllTests() {
  console.log('=== 横屏功能综合测试 ===');
  
  testLandscapeManager();
  
  setTimeout(() => {
    testContainerDetection();
  }, 1000);
  
  setTimeout(() => {
    testStyleApplication();
  }, 2000);
  
  setTimeout(() => {
    simulateUserInteraction();
  }, 3000);
}

// 导出测试函数到全局作用域
if (typeof window !== 'undefined') {
  window.testLandscapeManager = testLandscapeManager;
  window.testContainerDetection = testContainerDetection;
  window.testStyleApplication = testStyleApplication;
  window.simulateUserInteraction = simulateUserInteraction;
  window.runAllTests = runAllTests;
  
  console.log('横屏测试函数已加载，可以在控制台中运行：');
  console.log('- testLandscapeManager() - 测试横屏管理器');
  console.log('- testContainerDetection() - 测试容器检测');
  console.log('- testStyleApplication() - 测试样式应用');
  console.log('- simulateUserInteraction() - 模拟用户交互');
  console.log('- runAllTests() - 运行所有测试');
}

// 如果在 Node.js 环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testLandscapeManager,
    testContainerDetection,
    testStyleApplication,
    simulateUserInteraction,
    runAllTests
  };
}
