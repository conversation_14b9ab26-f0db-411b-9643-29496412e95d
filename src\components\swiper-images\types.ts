export interface ImageItem {
  /** 图片地址 */
  src: string;
  /** 图片描述/标题 */
  alt?: string;
  /** 缩略图地址，用于轮播显示 */
  thumbnail?: string;
}

export interface SwiperImagesProps {
  /** 图片数组 */
  images: ImageItem[];
  /** 是否自动播放 */
  autoplay?: boolean;
  /** 自动播放间隔时间（毫秒），默认3000ms */
  autoplayInterval?: number;
  /** 是否显示指示器 */
  showIndicator?: boolean;
  /** 是否循环播放 */
  loop?: boolean;
  /** 轮播图高度 */
  height?: string | number;
  /** 点击图片进入查看模式时是否暂停自动播放 */
  pauseOnView?: boolean;
  /** 退出查看模式后是否恢复自动播放 */
  resumeOnExit?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 图片加载失败时的占位图 */
  fallbackSrc?: string;
  /** 是否显示自定义工具栏（旋转、放大、缩小、全屏） */
  showToolbar?: boolean;
  /** 是否显示旋转按钮 */
  showRotateButton?: boolean;
  /** 是否显示缩放按钮 */
  showScaleButtons?: boolean;
  /** 是否显示全屏按钮 */
  showFullscreenButton?: boolean;
  /** 是否显示横屏按钮 */
  showLandscapeButton?: boolean;
  /** 是否显示查看模式播放按钮 */
  showViewerPlayButton?: boolean;
  /** 查看模式自动播放间隔时间（毫秒），默认5000ms */
  viewerAutoplayInterval?: number;
  /** 查看模式是否循环播放 */
  viewerAutoplayLoop?: boolean;
  /** 用户交互时是否暂停查看模式播放 */
  pauseViewerOnInteraction?: boolean;
  /** 轮播切换回调 */
  onChange?: (index: number) => void;
  /** 图片点击回调 */
  onImageClick?: (index: number, image: ImageItem) => void;
}

export interface SwiperImagesRef {
  /** 切换到指定索引的图片 */
  swipeTo: (index: number) => void;
  /** 开始自动播放 */
  startAutoplay: () => void;
  /** 停止自动播放 */
  stopAutoplay: () => void;
  /** 获取当前图片索引 */
  getCurrentIndex: () => number;
}
