$color-primary: var(--znmui-color-primary, #3569fd) !default;
$color-primary-hover: var(--znmui-color-primary-light-5, #5e8fff) !default;
$color-success: var(--znmui-color-success, #00b578) !default;
$color-warning: var(--znmui-color-warning, #ff8f1f) !default;
$color-danger: var(--znmui-color-error, #ff3141) !default;

$white: #fff !default;
$black: #000 !default;

// 背景色
$color-bg-gray: #f4f4f4; // 背景色

// 圆角
$radius-xs: var(--znmui-radius-xs, 2px) !default;
$radius-small: var(--znmui-radius-s, 4px) !default;
$radius-base: var(--znmui-radius-m, 8px) !default; // 默认圆角
$radius-large: var(--znmui-radius-l, 12px) !default;

// 字体大小
$font-size-small: var(--znmui-font-size-4, 12px) !default;
$font-size-base: var(--znmui-font-size-6, 14px) !default; // 默认字体大小
$font-size-large: var(--znmui-font-size-8, 16px) !default;
$font-size-xlarge: var(--znmui-font-size-9, 17px) !default;
$font-size-xl: var(--znmui-font-size-12, 20px) !default;
// 文本
$color-text: var(--znmui-black-alpha-88, rgba(0, 0, 0, 0.88)) !default;
$color-text-primary: var(--znmui-color-text, #333333) !default; // 一级文本色
$color-text-secondary: var(--znmui-color-text-secondary, #666666) !default; // 二级文本色
$color-text-secondary-2: var(--znmui-color-text-secondary-2, rgba(0, 0, 0, 0.45)) !default; // 二级文本色
$color-text-tertiary-3: var(--znmui-color-text-tertiary-3, rgba(0, 0, 0, 0.45)) !default; // 二级文本色
$color-text-tertiary: var(--znmui-color-weak, #999999) !default; // 三级文本色
$color-primary-background-hover: var(--znmui-color-primary-light-2, #d9e8ff) !default;
$color-primary-text: var(--znmui-color-primary, #3569fd) !default;
$color-warning-text: $color-warning;
$color-warning-text-hover: var(--znmui-color-warning-light-5, #ffc53d) !default;
$color-warning-text-active: var(--znmui-color-warning-deep, #d48806) !default;
$color-primary-border-hover: var(--znmui-color-primary-light-4, #87afff) !default;
// 填充
$color-fill-tertiary: var(--znmui-black-alpha-04, rgba(0, 0, 0, 0.04)) !default; // 三级填充色

// 标题
$heading-size-2: 30px;
$heading-size-3: 24px;

// 描边
$color-border-primary: var(--znmui-color-border, #eeeeee) !default; // 一级边框色
$color-border-secondary: var(--znmui-color-light, #cccccc) !default; // 二级边框色

// 阴影
$box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12),
  0 9px 28px 8px rgba(0, 0, 0, 0.05); // 一级阴影
$box-shadow-secondary: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12),
  0 9px 28px 8px rgba(0, 0, 0, 0.05); // 二级阴影

// Icon图标
$color-icon-outlined: var(--znmui-black-alpha-85, rgba(0, 0, 0, 0.85)) !default; // 线框风格
$color-icon-filled: var(--znmui-black-alpha-85, rgba(0, 0, 0, 0.85)) !default; // 实底风格

// margin
$margin-xl: 28px;
$margin-xxl: 32px;
$margin-large: 24px;
$margin-middle: 16px;
$margin-small: 12px;
$margin-xs: 8px;
$margin-xss: 4px;

// padding
$padding-xl: 28px;
$padding-xxl: 32px;
$padding-large: 24px;
$padding-middle: 16px;
$padding-small: 12px;
$padding-xs: 8px;
$padding-xss: 4px;

// line-height
$line-height-large: 28px;
$line-height-middle: 24px;
$line-height-small: 22px;
$line-height-xs: 20px;

// result-icon
$result-icon-size: 50px;

$mainColor: #1677ff;

$footer-height: 56px;
$radius-middle: 8px;
$radius-large: 12px;

$icon-font-size: 24px;
$icon-font-size-small: 12px;
$icon-font-size-middle: 16px;
$icon-font-size-large: 20px;

//待办 Popup 高度
$popup-height: 80vh;

//nav-bar 高度
$nav-bar-height: 44px;
//公众号nav-bar 高度
$nav-bar-height-h5: 40px;

