import React, { useEffect, useState, useCallback } from 'react';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';
import { Card, Toast } from 'antd-mobile';
import HeaderNavbar from '@/components/header-navbar';
import ClientRouter from '@/base/client/client-router';
import { useSearchParams } from 'react-router-dom';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils/to';
import { gruops } from './mock';
import SwiperImagesDemo from '@/components/swiper-images/demo';

const LogsDetail: React.FC = () => {
  const [searchParams] = useSearchParams();
  const clientRouter = ClientRouter.getRouter();
  const session_id = searchParams.get('session_id');
  const [logGroups, setLogGroups] = useState<SuperviseModule.LogsDetailGroups[]>(
    gruops as SuperviseModule.LogsDetailGroups[]
  );
  const getLogDetail = async () => {
    if (session_id) {
      const [err, res] = await to(superviseService.superviseLogDetail({ session_id }));
      if (err) return;
      setLogGroups(res.data?.groups);
    }
  };
  useEffect(() => {
    getLogDetail();
  }, [session_id]);
  console.log('@@@@logGroups', logGroups);
  return (
    <div className={styles.logsDetail}>
      <SwiperImagesDemo />
    </div>
  );
};

export default observer(LogsDetail);
