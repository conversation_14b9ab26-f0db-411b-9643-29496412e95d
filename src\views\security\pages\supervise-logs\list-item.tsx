import React from 'react';
import { observer } from 'mobx-react';
import { Card, Button } from 'antd-mobile';
import CardItem from '@/components/card-item';
import { getTimeDifference } from '@/utils/time';
import { useMemoizedFn } from 'ahooks';
import { AiFillWarning } from 'react-icons/ai';
import dayjs from 'dayjs';
import ClientRouter from '@/base/client/client-router';
import { SECURITY_ROUTER } from '@/constants/manage';

import styles from './styles.module.scss';
interface SuperviseLogsItemProps {
  data: SuperviseModule.LogsItem;
}

const SuperviseLogsItem: React.FC<SuperviseLogsItemProps> = (props) => {
  const { data } = props;
  const clientRouter = ClientRouter.getRouter();
  const formatTime = useMemoizedFn((val) => {
    return dayjs(val * 1000).format('YYYY-MM-DD HH:mm:ss');
  });
  return (
    <Card className={styles.logItem}>
      <div className={styles.logItemHeader}>
        <span>{data?.account_name}</span>
        {!!data?.warning_count && (
          <span className={styles.warningCount}>
            <AiFillWarning color="var(--adm-color-danger)" />
            <span className={styles.warningCountText}>风险事件：{data?.warning_count}</span>
          </span>
        )}
      </div>
      <CardItem label="记录平台账号" content={data?.account_name} />
      <CardItem label="开始时间" content={formatTime(data?.start_time)} />
      <CardItem label="结束时间" content={`${formatTime(data?.end_time) || '至今'}`} />
      <CardItem
        label="记录时长"
        content={!data?.end_time ? '记录中' : getTimeDifference(data?.start_time, data?.end_time)}
      />
      <footer>
        <Button
          onClick={() => {
            clientRouter.push(`${SECURITY_ROUTER.LOGS_DETAIL}?session_id=${data?.session_id}`);
          }}
          color="primary"
        >
          查看详情
        </Button>
      </footer>
    </Card>
  );
};

export default observer(SuperviseLogsItem);
