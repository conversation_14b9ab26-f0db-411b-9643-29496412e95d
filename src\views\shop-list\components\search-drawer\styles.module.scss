.list {
  overflow-y: auto;
  padding: 12px;
  padding-top: calc(var(--safe-top) + 12px);
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
    color: #222222;
    border-top: 1px solid #f6f6f6;

    &:first-child {
      color: #666666;
      border: none;
    }

    & > div {
      display: flex;
    }

    .leftBox {
      display: flex;
      align-items: center;

      .logo {
        width: 15px;
        height: 15px;
        margin-right: 6px;
        background-image: url('./img/TagSelectDark.png');
        background-size: 15px 15px;
      }

      .title {
        // flex: 1;
        font-size: 14px;
        margin-top: 2px;
      }
    }

    .rightBox {
      .refreshImg {
        width: 14px;
        height: 14px;
      }
    }
  }

  .tagHeader {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
  }

  .platHeader {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 15px;
  }

  .showAll {
    max-height: 50vh;
    overflow-y: auto;
  }
}

.tagBox {
  display: flex;
  flex-wrap: wrap;

  margin-bottom: 15px;
  margin-top: 10px;
  max-height: 130px;
  // min-height: 130px;
  overflow: hidden;
  .tag {
    width: 26%;
    height: 17px;
    display: flex;
    justify-content: center;
    padding: 10px 5px;
    margin: 0 3.5px 7px 3.5px;
    background-color: #f5f5f5;
    border-radius: 4px;
    color: #000000e0;
    border: 1px solid transparent;

    .name {
      font-size: 14px;
      color: rgba(102, 102, 102, 1);

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .count {
      font-size: 14px;
      font-weight: 400;
      color: rgba(170, 170, 170, 1);
    }
  }

  .tagChecked {
    position: relative;
    background-color: #f1f5ff;
    border-color: #3c72f8; // 选中状态下的边框颜色

    // &::after {
    //   content: '';
    //   position: absolute;
    //   right: 3px;
    //   bottom: 3px;
    //   height: 6px;
    //   width: 6px;
    //   // background: url('./img/checked.png') no-repeat center;
    //   background-size: contain;
    // }

    .name,
    .count {
      color: #3c72f8;
    }
  }
}

.noTagTip {
  margin: auto;
  text-align: center;
  font-size: 16px;
  line-height: 1.5;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: rgba(102, 102, 102, 1);
}

.whiteSpaceFive {
  height: 5px;
}

.popupBody {
  display: flex;
  flex-direction: column;
  height: 100%;
  .footer {
    padding: 0 12px 12 12px;
    display: flex;
    flex: 1;
    align-items: end;
    justify-content: space-between;
    padding-bottom: calc(var(--safe-bottom) + 12px);

    .okBtn {
      margin-left: 10px;
    }
    :global {
      .adm-button {
        width: 120px;
        margin: 0 $margin-xs;
        font-weight: 400;
      }

      .adm-button-default {
        color: $color-primary;
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }
}
