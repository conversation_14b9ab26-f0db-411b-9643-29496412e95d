import React, { Component, useState } from 'react';
import { Store } from './store';
import styles from './styles.module.scss';
import { observable, toJS } from 'mobx';
import { observer } from 'mobx-react';
import NoSearchData from './img/no-search-data.png';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import dayjs from 'dayjs';

const OperateTypeName = {
  0: '复制',
  1: '下载',
  2: '上传',
  3: '打印',
  4: '打开开发者工具(F12)',
  6: '访问网页',
  10: '键盘输入',
  11: '点击鼠标左键',
  12: '点击鼠标右键',
}

@observer
export default class WarningDetail extends Component {
  @observable store = new Store();
  @observable screenshotOpen = false;
  constructor(props) {
    super(props);
  }
  init = () => {
  };


  render() {
    const { detailData, isExceedLimit, notAuth } = this.store;
    const group = detailData?.groups?.[0];
    const event = group?.events?.[0];
    const image_url = event?.image_url;

    if (isExceedLimit) {
      return (
        <div className={styles.detailBox}>
          <div
            style={{
              textAlign: "center"
            }}
          >
            <div>
              <img src={NoSearchData}></img>
            </div>
            非会员监管记录可查看1次，建议前往电脑端开通会员享无限查看次数
          </div>
        </div>
      );
    }

    if (notAuth) {
      return (
        <div className={styles.detailBox}>
          <div
            style={{
              textAlign: "center"
            }}
          >
            <div>
              <img src={NoSearchData}></img>
            </div>
            无权限查看
          </div>
        </div>
      );
    }

    if (!detailData) {
      return (
        <div className={styles.detailBox}>
          <div
            style={{
              textAlign: "center"
            }}
          >
            加载中
          </div>
        </div>
      );
    }

    return (
      <div className={styles.detailBox}>
        <div className={styles.lineBox}>
          <div className={styles.lineLeft}>
            预警账号：
          </div>
          <div className={styles.lineRight}>
            {detailData?.account_name}
          </div>
        </div>
        <div className={styles.lineBox}>
          <div className={styles.lineLeft}>
            预警事件：
          </div>
          <div className={styles.lineRight}>
            {OperateTypeName[event?.type]}
          </div>
        </div>
        <div className={styles.lineBox}>
          <div className={styles.lineLeft}>
            预警网页：
          </div>
          <div className={styles.lineRight}>
            {`${group?.name}(${group?.url})`}
          </div>
        </div>
        <div className={styles.lineBox}>
          <div className={styles.lineLeft}>
            预警成员：
          </div>
          <div className={styles.lineRight}>
            {`${detailData?.user_username}(${detailData?.user_name})`}
          </div>
        </div>
        <div className={styles.lineBox}>
          <div className={styles.lineLeft}>
            预警时间：
          </div>
          <div className={styles.lineRight}>
            {dayjs.unix(event?.create_time).format('YYYY.MM.DD HH:mm:ss')}
          </div>
        </div>
        {
          Number(event.type) !== 6 ? (
            <div className={styles.lineBox}>
              <div className={styles.lineLeft}>
                操作截图：
              </div>
              {
                event?.is_sensitive ? (
                  <div className={styles.lineRight}>
                    敏感页面，不支持查看
                  </div>
                ) : null
              }
            </div>
          ) : null
        }
        {
          (!event?.is_sensitive && Number(event.type) !== 6) ? (
            <div
              className={styles.screenshot}
              onClick={() => {
                this.screenshotOpen = true;
              }}
            >
              <img src={image_url} alt="" />
            </div>
          ) : null
        }

        {this.screenshotOpen && (
          <Lightbox
            mainSrc={image_url}
            onCloseRequest={() => {
              this.screenshotOpen = false;
            }}
            onMovePrevRequest={() => {
            }}
            onMoveNextRequest={() => {
            }}
          />
        )}

      </div>
    );
  }
}


